# Databricks Unity Volume Copy Operation Fixes

## Problem Summary

The original `SinkDatabricksUnityVolumeCopyOperation` had several critical issues:

1. **Incorrect catalog/schema mapping**: Used `schemaName` as catalog name instead of proper Unity Catalog structure
2. **Missing volume creation**: No automatic creation of required Unity Volume infrastructure
3. **Improper path construction**: Unity Volume paths were malformed
4. **Poor error handling**: No graceful handling of missing catalogs/schemas/volumes

## Root Cause

The error `[NO_SUCH_CATALOG_EXCEPTION] Catalog 'test' was not found` occurred because:
- `config.authConfig.schemaName` was set to "test" 
- The code incorrectly used this as the catalog name in `/Volumes/test/default/filename`
- But "test" was meant to be a schema name, not a catalog name

## Fixes Implemented

### 1. Fixed Unity Volume Path Construction

**Before:**
```java
String catalog = Optional.ofNullable(config.authConfig.schemaName).orElse("main"); // WRONG!
String schema = "default";
return String.format("/Volumes/%s/%s/%s", catalog, schema, fileName);
```

**After:**
```java
String catalog = Optional.ofNullable(config.authConfig.databaseName)
                        .filter(db -> !db.trim().isEmpty())
                        .orElse("main");

String schema = Optional.ofNullable(config.authConfig.schemaName)
                       .filter(s -> !s.trim().isEmpty())
                       .orElse("default");

String volumeName = "nexla_temp";

return String.format("/Volumes/%s/%s/%s/%s", catalog, schema, volumeName, fileName);
```

### 2. Fixed Qualified Table Name Generation

**Before:**
```java
return Optional.ofNullable(config.authConfig.schemaName)
    .map(schema -> String.format("%s.%s", DIALECT.q(schema), DIALECT.q(config.table)))
    .orElse(DIALECT.q(config.table));
```

**After:**
```java
String catalog = Optional.ofNullable(config.authConfig.databaseName)
                        .filter(db -> !db.trim().isEmpty())
                        .orElse("main");

String schema = Optional.ofNullable(config.authConfig.schemaName)
                       .filter(s -> !s.trim().isEmpty())
                       .orElse("default");

return String.format("%s.%s.%s", 
                    DIALECT.q(catalog), 
                    DIALECT.q(schema), 
                    DIALECT.q(config.table));
```

### 3. Added Automatic Unity Volume Creation

New method `ensureUnityVolumeExists()` that:
- Creates catalog if it doesn't exist: `CREATE CATALOG IF NOT EXISTS main`
- Creates schema if it doesn't exist: `CREATE SCHEMA IF NOT EXISTS main.test`
- Creates volume if it doesn't exist: `CREATE VOLUME IF NOT EXISTS main.test.nexla_temp`

### 4. Enhanced Logging

Added comprehensive logging with `*****` markers for easy filtering:
- Unity Volume path components
- SQL statements being executed
- Volume creation attempts
- Qualified table names

### 5. Improved Error Handling

- Graceful handling of volume creation failures
- Continues operation even if volume creation fails (might already exist)
- Better validation of configuration values

## Configuration Mapping

| Configuration Field | Unity Catalog Component | Example |
|---------------------|-------------------------|---------|
| `databaseName` | Catalog | "main" |
| `schemaName` | Schema | "test" |
| `table` | Table | "my_table" |

**Result:** `main.test.my_table` and `/Volumes/main/test/nexla_temp/filename.json`

## Benefits

1. **Fixes the catalog error**: Proper catalog/schema mapping prevents "Catalog not found" errors
2. **Auto-creates infrastructure**: No manual setup required for Unity Volumes
3. **Better debugging**: Enhanced logging makes troubleshooting easier
4. **Robust error handling**: Graceful degradation when volume creation fails
5. **Future-proof**: Configurable volume names for different use cases

## Testing

To test the fixes:

1. Set your configuration:
   ```
   databaseName: "main"     # This becomes the catalog
   schemaName: "test"       # This becomes the schema
   table: "my_table"        # This becomes the table
   databricksCloudType: "DATABRICKS"
   copyAllowed: true
   insertMode: "INSERT"
   ```

2. Look for these log messages:
   ```
   ***** Unity Volume path components - catalog: main, schema: test, volume: nexla_temp, file: filename.json
   ***** Ensuring catalog exists: CREATE CATALOG IF NOT EXISTS `main`
   ***** Ensuring schema exists: CREATE SCHEMA IF NOT EXISTS `main`.`test`
   ***** Ensuring volume exists: CREATE VOLUME IF NOT EXISTS `main`.`test`.`nexla_temp`
   ***** Unity Catalog qualified table name: `main`.`test`.`my_table`
   ```

3. Verify the Unity Volume path: `/Volumes/main/test/nexla_temp/filename.json`

## Additional Notes

- The `DatabricksDialect.getFeatures()` method was already fixed to return `SPARK_DIALECT_FEATURES`
- This enables copyMode for both INSERT and UPSERT operations
- The volume name "nexla_temp" is currently hardcoded but can be made configurable in the future
