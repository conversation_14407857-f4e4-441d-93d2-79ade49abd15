package connect.jdbc.sink.dialect.copy;

import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DatabricksDialect;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Implementation of SinkCopyOperation for Databricks Unity Volume.
 * This class handles the copy operation for Databricks when the cloud type is DATABRICKS.
 */
public class SinkDatabricksUnityVolumeCopyOperation extends BaseSinkCopyOperation {

    private static final Logger logger = LoggerFactory.getLogger(SinkDatabricksUnityVolumeCopyOperation.class);
    private static final DatabricksDialect DIALECT = new DatabricksDialect();

    // Store the local file for upload to Unity Volume
    private File localFileToUpload;

    @Override
    @SneakyThrows
    protected void executeCopyCommand(String volumeLocation, Connection connection, Optional<ReplicationContext> replicationContext) {
        // Validate Unity Volume components exist before using them
        validateUnityVolumeExists(connection);

        // Upload the local file to Unity Volume using PUT command
        if (localFileToUpload != null) {
            uploadFileToUnityVolume(connection, localFileToUpload, volumeLocation);
        }

        final boolean tableExist = isTableExist(connection, config.table);
        logger.info("***** Delta table {} already exists: {}", config.table, tableExist);
        if (tableExist) {
            executeStatement(connection, copyCommand(schema, volumeLocation, config.table, replicationContext));
        } else {
            executeStatement(connection, createDeltaTableCommand(volumeLocation));
        }
    }

    @Override
    @SneakyThrows
    protected void executeUpsert(Statement st, File localFile, String tempTable, Optional<ReplicationContext> replicationContext) {
        Connection conn = st.getConnection();

        // Validate Unity Volume components exist before using them
        validateUnityVolumeExists(conn);

        final boolean tableExist = isTableExist(conn, config.table);
        logger.info("***** Delta table {} already exists: {}", config.table, tableExist);
        if (tableExist) {
            logUpdated(st.executeUpdate(logSql(mergeCommand(config.table, config.primaryKey, tempFileLocation(localFile.getName())))));
        } else {
            executeStatement(conn, createDeltaTableCommand(tempFileLocation(localFile.getName())));
        }
    }

    public static boolean isTableExist(Connection connection, String table) throws java.sql.SQLException {
        boolean tableExist = false;
        try (Statement checkStatement = connection.createStatement();
             ResultSet tablesRS = checkStatement.executeQuery("SHOW TABLES")) {
            while (tablesRS.next()) {
                if (table.equals(tablesRS.getString("tableName"))) {
                    tableExist = true;
                    break;
                }
            }
        }
        return tableExist;
    }

    @Override
    public DbDialect dbDialect() {
        return DIALECT;
    }

    @Override
    @SneakyThrows
    protected void runUpsert(Optional<List<String>> columnsOpt, File localFile, Connection conn, Optional<ReplicationContext> replicationContext) {
        Statement st = conn.createStatement();
        executeUpsert(st, localFile, StringUtils.EMPTY, replicationContext);
    }

    private String mergeCommand(String qualifiedTableName, List<String> primaryKey, String tempFileLocation) {
        final String condition = primaryKey.stream()
            .map(k -> String.format("target.%s = source.%s", k, k))
            .collect(Collectors.joining(" AND "));
        return String.format("MERGE INTO %s target\n" +
                " USING json.`%s` source\n" +
                " ON %s\n" +
                " WHEN MATCHED THEN\n" +
                "  UPDATE SET *\n" +
                " WHEN NOT MATCHED\n" +
                "  THEN INSERT *"
            , qualifiedTableName, tempFileLocation, condition);
    }

    @Override
    protected void initCloudObjectStoreClient() {
        // Unity Volume doesn't require a cloud object store client
        // as it's managed by Databricks
    }

    @Override
    protected void deleteCloudFile(File localFile) {
        // Unity Volume files are managed by Databricks
        // No explicit deletion needed
        logger.info("Unity Volume files are managed by Databricks, no explicit deletion needed for: {}", localFile.getName());
    }

    @Override
    public void uploadFile(File localFile) {
        // For Unity Volume, we need to copy the local file to the Unity Volume using PUT command
        logger.info("***** Unity Volume: copying local file {} to Unity Volume", localFile.getName());

        String volumePath = tempFileLocation(localFile.getName());
        logger.info("***** Unity Volume path will be: {}", volumePath);

        // Store the local file for later upload during the copy operation
        // Unity Volumes require the file to be uploaded using PUT command before COPY INTO
        this.localFileToUpload = localFile;
        logger.info("***** Unity Volume: local file stored for upload during copy operation");
    }

    @Override
    String tempFileLocation(String fileName) {
        // Format for Unity Volume path: /Volumes/<catalog>/<schema>/<volume>/<path>

        // For Unity Catalog, catalog should come from databaseName or default to "main"
        // schemaName should be used as the actual schema, not catalog
        String catalog = Optional.ofNullable(config.authConfig.databaseName)
                                .filter(db -> !db.trim().isEmpty())
                                .orElse("main");

        String schema = Optional.ofNullable(config.authConfig.schemaName)
                               .filter(s -> !s.trim().isEmpty())
                               .orElse("default");

        // Use a default volume name for temporary files
        // This could be made configurable in the future
        String volumeName = "nexla_temp";

        logger.info("***** Unity Volume path components - catalog: {}, schema: {}, volume: {}, file: {}",
                   catalog, schema, volumeName, fileName);

        // Construct the Unity Volume path
        return String.format("/Volumes/%s/%s/%s/%s",
                             catalog,
                             schema,
                             volumeName,
                             fileName);
    }

    @Override
    public String copyCommand(Schema schema, String volumeLocation, String table, Optional<ReplicationContext> replicationContext) {
        return String.format("COPY INTO %s\n" +
                "  FROM '%s'\n" +
                "  FILEFORMAT = JSON\n",
            getQualifiedTableName(), volumeLocation);
    }

    private String createDeltaTableCommand(String volumeLocation) {
        // For Unity Volume, we don't need to specify a LOCATION clause
        // as the table will be created in the Unity Catalog
        return String.format("CREATE TABLE %s\n" +
                "  USING delta\n" +
                "  AS SELECT *\n" +
                "  FROM json.`%s`\n",
            getQualifiedTableName(), volumeLocation);
    }

    @Override
    protected String createCommand(String tempTable, String qualifiedTableName) {
        // No temp table, we use databricks merge for upsert
        return "";
    }

    @Override
    public void dropTempTable(Statement stmt, String tempTable) {
        // No temp table, we use databricks merge for upsert
    }

    private String getQualifiedTableName() {
        // For Unity Catalog, we need catalog.schema.table format
        String catalog = Optional.ofNullable(config.authConfig.databaseName)
                                .filter(db -> !db.trim().isEmpty())
                                .orElse("main");

        String schema = Optional.ofNullable(config.authConfig.schemaName)
                               .filter(s -> !s.trim().isEmpty())
                               .orElse("default");

        String qualifiedName = String.format("%s.%s.%s",
                                            DIALECT.q(catalog),
                                            DIALECT.q(schema),
                                            DIALECT.q(config.table));

        logger.info("***** Unity Catalog qualified table name: {}", qualifiedName);
        return qualifiedName;
    }

    @Override
    protected String getTempFilePrefix() {
        return "unity_volume";
    }

    private void executeStatement(Connection connection, String sql) throws java.sql.SQLException {
        logger.info("***** Executing SQL: {}", sql);
        try (Statement statement = connection.createStatement()) {
            statement.execute(sql);
        }
    }

    /**
     * Validates that the Unity Catalog, Schema, and Volume exist for temporary file operations
     * Throws an exception if any required component doesn't exist
     */
    private void validateUnityVolumeExists(Connection connection) throws java.sql.SQLException {
        try {
            String catalog = Optional.ofNullable(config.authConfig.databaseName)
                                    .filter(db -> !db.trim().isEmpty())
                                    .orElse("main");

            String schema = Optional.ofNullable(config.authConfig.schemaName)
                                   .filter(s -> !s.trim().isEmpty())
                                   .orElse("default");

            String volumeName = "nexla_temp";

            logger.info("***** Validating Unity Catalog components - catalog: {}, schema: {}, volume: {}",
                       catalog, schema, volumeName);

            // Check if catalog exists
            if (!catalogExists(connection, catalog)) {
                String errorMsg = String.format("Unity Catalog '%s' does not exist. Please create it first using: CREATE CATALOG %s",
                                               catalog, DIALECT.q(catalog));
                logger.error("***** {}", errorMsg);
                throw new java.sql.SQLException(errorMsg);
            }
            logger.info("***** Catalog '{}' exists", catalog);

            // Check if schema exists
            if (!schemaExists(connection, catalog, schema)) {
                String errorMsg = String.format("Unity Schema '%s.%s' does not exist. Please create it first using: CREATE SCHEMA %s.%s",
                                               catalog, schema, DIALECT.q(catalog), DIALECT.q(schema));
                logger.error("***** {}", errorMsg);
                throw new java.sql.SQLException(errorMsg);
            }
            logger.info("***** Schema '{}.{}' exists", catalog, schema);

            // Create volume if it doesn't exist (only volume creation is allowed)
            String createVolumeSql = String.format("CREATE VOLUME IF NOT EXISTS %s.%s.%s",
                                                  DIALECT.q(catalog), DIALECT.q(schema), DIALECT.q(volumeName));
            logger.info("***** Ensuring volume exists: {}", createVolumeSql);
            executeStatement(connection, createVolumeSql);
            logger.info("***** Volume '{}.{}.{}' is ready", catalog, schema, volumeName);

        } catch (java.sql.SQLException e) {
            // Re-throw SQL exceptions as they contain validation errors
            throw e;
        } catch (Exception e) {
            // Wrap other exceptions to provide better error context
            String errorMsg = String.format("Failed to validate Unity Volume components: %s", e.getMessage());
            logger.error("***** {}", errorMsg, e);
            throw new java.sql.SQLException(errorMsg, e);
        }
    }

    /**
     * Checks if a catalog exists in Unity Catalog
     */
    private boolean catalogExists(Connection connection, String catalogName) throws java.sql.SQLException {
        String checkCatalogSql = "SHOW CATALOGS";
        logger.debug("***** Checking catalog existence: {}", checkCatalogSql);

        try (Statement statement = connection.createStatement();
             ResultSet rs = statement.executeQuery(checkCatalogSql)) {
            while (rs.next()) {
                String existingCatalog = rs.getString("catalog"); // Databricks uses 'catalog' column
                if (catalogName.equals(existingCatalog)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * Checks if a schema exists in the specified catalog
     */
    private boolean schemaExists(Connection connection, String catalogName, String schemaName) throws java.sql.SQLException {
        // Use SHOW SCHEMAS FROM catalog instead of SHOW SCHEMAS IN catalog for better compatibility
        String checkSchemaSql = String.format("SHOW SCHEMAS FROM %s", DIALECT.q(catalogName));
        logger.debug("***** Checking schema existence: {}", checkSchemaSql);

        try (Statement statement = connection.createStatement();
             ResultSet rs = statement.executeQuery(checkSchemaSql)) {
            while (rs.next()) {
                String existingSchema = rs.getString("databaseName"); // Databricks uses 'databaseName' column
                if (schemaName.equals(existingSchema)) {
                    return true;
                }
            }
            return false;
        }
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
        // No specific resources to close for Unity Volume
    }
}
