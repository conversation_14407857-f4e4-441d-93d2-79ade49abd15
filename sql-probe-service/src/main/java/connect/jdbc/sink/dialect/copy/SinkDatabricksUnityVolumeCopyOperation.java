package connect.jdbc.sink.dialect.copy;

import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DatabricksDialect;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Implementation of SinkCopyOperation for Databricks Unity Volume.
 * This class handles the copy operation for Databricks when the cloud type is DATABRICKS.
 */
public class SinkDatabricksUnityVolumeCopyOperation extends BaseSinkCopyOperation {

    private static final Logger logger = LoggerFactory.getLogger(SinkDatabricksUnityVolumeCopyOperation.class);
    private static final DatabricksDialect DIALECT = new DatabricksDialect();

    @Override
    @SneakyThrows
    protected void executeCopyCommand(String volumeLocation, Connection connection, Optional<ReplicationContext> replicationContext) {
        final boolean tableExist = isTableExist(connection, config.table);
        logger.info("delta table {} already exists: {}", config.table, tableExist);
        if (tableExist) {
            executeStatement(connection, copyCommand(schema, volumeLocation, config.table, replicationContext));
        } else {
            executeStatement(connection, createDeltaTableCommand(volumeLocation));
        }
    }

    @Override
    @SneakyThrows
    protected void executeUpsert(Statement st, File localFile, String tempTable, Optional<ReplicationContext> replicationContext) {
        Connection conn = st.getConnection();
        final boolean tableExist = isTableExist(conn, config.table);
        logger.info("delta table {} already exists: {}", config.table, tableExist);
        if (tableExist) {
            logUpdated(st.executeUpdate(logSql(mergeCommand(config.table, config.primaryKey, tempFileLocation(localFile.getName())))));
        } else {
            executeStatement(conn, createDeltaTableCommand(tempFileLocation(localFile.getName())));
        }
    }

    public static boolean isTableExist(Connection connection, String table) throws java.sql.SQLException {
        boolean tableExist = false;
        Statement checkStatement = connection.createStatement();
        final ResultSet tablesRS = checkStatement.executeQuery("SHOW TABLES");
        while (tablesRS.next()) {
            if (table.equals(tablesRS.getString("tableName"))) {
                tableExist = true;
                break;
            }
        }
        return tableExist;
    }

    @Override
    public DbDialect dbDialect() {
        return DIALECT;
    }

    @Override
    @SneakyThrows
    protected void runUpsert(Optional<List<String>> columnsOpt, File localFile, Connection conn, Optional<ReplicationContext> replicationContext) {
        Statement st = conn.createStatement();
        executeUpsert(st, localFile, StringUtils.EMPTY, replicationContext);
    }

    private String mergeCommand(String qualifiedTableName, List<String> primaryKey, String tempFileLocation) {
        final String condition = primaryKey.stream()
            .map(k -> String.format("target.%s = source.%s", k, k))
            .collect(Collectors.joining(" AND "));
        return String.format("MERGE INTO %s target\n" +
                " USING json.`%s` source\n" +
                " ON %s\n" +
                " WHEN MATCHED THEN\n" +
                "  UPDATE SET *\n" +
                " WHEN NOT MATCHED\n" +
                "  THEN INSERT *"
            , qualifiedTableName, tempFileLocation, condition);
    }

    @Override
    protected void initCloudObjectStoreClient() {
        // Unity Volume doesn't require a cloud object store client
        // as it's managed by Databricks
    }

    @Override
    protected void deleteCloudFile(File localFile) {
        // Unity Volume files are managed by Databricks
        // No explicit deletion needed
        logger.info("Unity Volume files are managed by Databricks, no explicit deletion needed for: {}", localFile.getName());
    }

    @Override
    public void uploadFile(File localFile) {
        // For Unity Volume, we don't need to upload the file to a cloud storage
        // The file will be directly referenced in the SQL commands
        logger.info("Unity Volume doesn't require explicit file upload, file will be referenced directly: {}", localFile.getName());
    }

    @Override
    String tempFileLocation(String fileName) {
        // Format for Unity Volume path: /Volumes/<catalog>/<schema>/<path>
        String catalog = Optional.ofNullable(config.authConfig.schemaName).orElse("main");
        String schema = "default"; // Default schema if not specified

        // Construct the Unity Volume path
        return String.format("/Volumes/%s/%s/%s",
                             catalog,
                             schema,
                             fileName);
    }

    @Override
    public String copyCommand(Schema schema, String volumeLocation, String table, Optional<ReplicationContext> replicationContext) {
        return String.format("COPY INTO %s\n" +
                "  FROM '%s'\n" +
                "  FILEFORMAT = JSON\n",
            getQualifiedTableName(), volumeLocation);
    }

    private String createDeltaTableCommand(String volumeLocation) {
        // For Unity Volume, we don't need to specify a LOCATION clause
        // as the table will be created in the Unity Catalog
        return String.format("CREATE TABLE %s\n" +
                "  USING delta\n" +
                "  AS SELECT *\n" +
                "  FROM json.`%s`\n",
            getQualifiedTableName(), volumeLocation);
    }

    @Override
    protected String createCommand(String tempTable, String qualifiedTableName) {
        // No temp table, we use databricks merge for upsert
        return "";
    }

    @Override
    public void dropTempTable(Statement stmt, String tempTable) {
        // No temp table, we use databricks merge for upsert
    }

    private String getQualifiedTableName() {
        return Optional.ofNullable(config.authConfig.schemaName)
            .map(schema -> String.format("%s.%s", DIALECT.q(schema), DIALECT.q(config.table)))
            .orElse(DIALECT.q(config.table));
    }

    @Override
    protected String getTempFilePrefix() {
        return "unity_volume";
    }

    private void executeStatement(Connection connection, String sql) throws java.sql.SQLException {
        logger.info("Executing SQL: {}", sql);
        try (Statement statement = connection.createStatement()) {
            statement.execute(sql);
        }
    }

    @Override
    public void close(Supplier<Connection> connectionProvider) {
        // No specific resources to close for Unity Volume
    }
}
